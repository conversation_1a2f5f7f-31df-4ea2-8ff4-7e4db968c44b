<template>
  <div class="customer-view">
    <!-- 统计卡片区域 -->
    <div class="stat-cards">
      <div class="stat-card">
        <div class="stat-icon blue">
          <i class="fas fa-users"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">
            <count-to class="num-chart" :end="customerStats.totalCustomers" unitClass="num-unit" :usegroup="true" />
            <span class="unit">家</span>
          </div>
          <div class="stat-label">客户总数</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon purple">
          <i class="fas fa-handshake"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">
            <count-to class="num-chart" :end="customerStats.activeCustomers" unitClass="num-unit" :usegroup="true" />
            <span class="unit">家</span>
          </div>
          <div class="stat-label">活跃客户</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon green">
          <i class="fas fa-chart-line"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">
            <count-to class="num-chart" :end="customerStats.totalOrders" unitClass="num-unit" :usegroup="true" />
            <span class="unit">单</span>
          </div>
          <div class="stat-label">总订单数</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon orange">
          <i class="fas fa-dollar-sign"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">
            <template v-if="isAmountVisible">
              <count-to class="num-chart" :end="customerStats.totalRevenue" unitClass="num-unit" :usegroup="true" :decimals="2" />
            </template>
            <template v-else>
              <span class="num-chart">******</span>
            </template>
            <span class="unit">万元</span>
            <i :class="isAmountVisible ? 'fas fa-eye' : 'fas fa-eye-slash'" class="toggle-eye"
              @click="toggleAmountVisibility"
              :title="isAmountVisible ? '隐藏金额' : '显示金额'"></i>
          </div>
          <div class="stat-label">总收入</div>
        </div>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 客户表格区域 -->
      <div class="customer-table-card">
        <div class="card-title">
          <div class="title-text">
            <i class="fas fa-list-ul"></i> 客户列表
          </div>
          <div class="table-actions">
            <div class="search-box">
              <i class="fas fa-search"></i>
              <input type="text" placeholder="搜索客户名称..." v-model="searchKeyword" @input="handleSearch">
            </div>
            <button class="refresh-btn" @click="refreshData" :disabled="loading">
              <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
            </button>
          </div>
        </div>

        <div class="customer-table" ref="customerTableRef">
          <table>
            <thead>
              <tr>
                <th>客户名称</th>
                <th>客户类型</th>
                <th>合作状态</th>
                <th>本月订单</th>
                <th>本月货量</th>
                <th>本月收入</th>
                <th>联系人</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <template v-for="(customer, index) in filteredCustomerList">
                <tr :key="index"
                    :class="{ 'highlighted-row': currentCustomerIndex === index }"
                    @click="selectCustomer(customer, index)">
                  <td>
                    <a @click.stop="openCustomerDetail(customer, index)"
                       style="cursor: pointer; font-weight: 600; color: #409eff;">
                      {{ customer.customerName }}
                    </a>
                  </td>
                  <td>
                    <div :class="['customer-type-tag', getCustomerTypeClass(customer.customerType)]">
                      <i :class="getCustomerTypeIcon(customer.customerType)"></i>
                      {{ customer.customerType }}
                    </div>
                  </td>
                  <td>
                    <div :class="['status-tag', getStatusClass(customer.cooperationStatus)]">
                      <i :class="getStatusIcon(customer.cooperationStatus)"></i>
                      {{ customer.cooperationStatus }}
                    </div>
                  </td>
                  <td>{{ customer.monthlyOrders }}</td>
                  <td>{{ customer.monthlyVolume }}万吨</td>
                  <td>
                    <span v-if="isAmountVisible">{{ customer.monthlyRevenue }}万元</span>
                    <span v-else>******</span>
                  </td>
                  <td>{{ customer.contactPerson }}</td>
                  <td>
                    <button class="detail-btn" @click.stop="openCustomerDetail(customer, index)">
                      <i class="fas fa-eye"></i>
                      详情
                    </button>
                  </td>
                </tr>
              </template>
            </tbody>
          </table>
          <Spin size="large" class="table-loading" fix v-if="loading"></Spin>
        </div>
      </div>
    </div>

    <!-- 客户详情弹窗 -->
    <div class="customer-detail-modal" v-if="showCustomerDetail" @click.self="closeCustomerDetail">
      <div class="customer-detail-container" :class="{ 'show': showCustomerDetail }">
        <div class="modal-header">
          <div class="customer-title">
            <i class="fas fa-user-tie"></i> {{ selectedCustomer.customerName }}
          </div>
          <button class="close-button" @click="closeCustomerDetail">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <div class="modal-content">
          <!-- 客户基本信息 -->
          <div class="info-section">
            <div class="section-header">
              <i class="fas fa-info-circle"></i> 客户基本信息
            </div>
            <div class="info-grid">
              <div class="info-item">
                <div class="info-label">客户名称</div>
                <div class="info-value">{{ selectedCustomer.customerName }}</div>
              </div>
              <div class="info-item">
                <div class="info-label">客户类型</div>
                <div class="info-value">
                  <span :class="['customer-type-tag-small', getCustomerTypeClass(selectedCustomer.customerType)]">
                    <i :class="getCustomerTypeIcon(selectedCustomer.customerType)"></i>
                    {{ selectedCustomer.customerType }}
                  </span>
                </div>
              </div>
              <div class="info-item">
                <div class="info-label">合作状态</div>
                <div class="info-value">
                  <span :class="['status-tag-small', getStatusClass(selectedCustomer.cooperationStatus)]">
                    <i :class="getStatusIcon(selectedCustomer.cooperationStatus)"></i>
                    {{ selectedCustomer.cooperationStatus }}
                  </span>
                </div>
              </div>
              <div class="info-item">
                <div class="info-label">联系人</div>
                <div class="info-value">{{ selectedCustomer.contactPerson }}</div>
              </div>
              <div class="info-item">
                <div class="info-label">联系电话</div>
                <div class="info-value">{{ selectedCustomer.contactPhone }}</div>
              </div>
              <div class="info-item">
                <div class="info-label">邮箱地址</div>
                <div class="info-value">{{ selectedCustomer.email }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CountTo from '_c/count-to'

export default {
  name: 'CustomeView',
  components: {
    CountTo
  },
  data() {
    return {
      loading: false,
      searchKeyword: '',
      currentCustomerIndex: 0,
      showCustomerDetail: false,
      selectedCustomer: {},
      isAmountVisible: true,

      // 客户统计数据
      customerStats: {
        totalCustomers: 156,
        activeCustomers: 89,
        totalOrders: 342,
        totalRevenue: 2856.78
      },

      // 客户列表数据
      customerList: [
        {
          id: 1,
          customerName: '中石化集团',
          customerType: '国有企业',
          cooperationStatus: '长期合作',
          monthlyOrders: 15,
          monthlyVolume: 125.6,
          monthlyRevenue: 456.78,
          contactPerson: '张经理',
          contactPhone: '138****8888',
          email: '<EMAIL>'
        },
        {
          id: 2,
          customerName: '中海油运输',
          customerType: '国有企业',
          cooperationStatus: '合作中',
          monthlyOrders: 12,
          monthlyVolume: 98.3,
          monthlyRevenue: 378.45,
          contactPerson: '李总监',
          contactPhone: '139****9999',
          email: '<EMAIL>'
        },
        {
          id: 3,
          customerName: '恒力石化',
          customerType: '民营企业',
          cooperationStatus: '新客户',
          monthlyOrders: 8,
          monthlyVolume: 67.2,
          monthlyRevenue: 234.56,
          contactPerson: '王主任',
          contactPhone: '137****7777',
          email: '<EMAIL>'
        },
        {
          id: 4,
          customerName: '浙江石化',
          customerType: '民营企业',
          cooperationStatus: '长期合作',
          monthlyOrders: 18,
          monthlyVolume: 156.8,
          monthlyRevenue: 567.89,
          contactPerson: '陈部长',
          contactPhone: '136****6666',
          email: '<EMAIL>'
        },
        {
          id: 5,
          customerName: '盛虹炼化',
          customerType: '民营企业',
          cooperationStatus: '合作中',
          monthlyOrders: 10,
          monthlyVolume: 89.4,
          monthlyRevenue: 312.34,
          contactPerson: '刘经理',
          contactPhone: '135****5555',
          email: '<EMAIL>'
        }
      ]
    }
  },
  computed: {
    filteredCustomerList() {
      if (!this.searchKeyword) {
        return this.customerList
      }
      return this.customerList.filter(customer =>
        customer.customerName.toLowerCase().includes(this.searchKeyword.toLowerCase()) ||
        customer.contactPerson.toLowerCase().includes(this.searchKeyword.toLowerCase())
      )
    }
  },
  methods: {
    // 搜索处理
    handleSearch() {
      // 搜索逻辑已在computed中处理
    },

    // 刷新数据
    refreshData() {
      this.loading = true
      // 模拟API调用
      setTimeout(() => {
        this.loading = false
        this.$Message.success('数据刷新成功')
      }, 1000)
    },

    // 选择客户
    selectCustomer(customer, index) {
      this.currentCustomerIndex = index
    },

    // 打开客户详情
    openCustomerDetail(customer, index) {
      this.selectedCustomer = customer
      this.showCustomerDetail = true
      document.body.classList.add('modal-open')
    },

    // 关闭客户详情
    closeCustomerDetail() {
      this.showCustomerDetail = false
      this.selectedCustomer = {}
      document.body.classList.remove('modal-open')
    },

    // 切换金额显示
    toggleAmountVisibility() {
      this.isAmountVisible = !this.isAmountVisible
    },

    // 获取客户类型样式类
    getCustomerTypeClass(type) {
      const typeMap = {
        '国有企业': 'state-owned',
        '民营企业': 'private',
        '外资企业': 'foreign',
        '合资企业': 'joint-venture'
      }
      return typeMap[type] || 'default'
    },

    // 获取客户类型图标
    getCustomerTypeIcon(type) {
      const iconMap = {
        '国有企业': 'fas fa-building',
        '民营企业': 'fas fa-industry',
        '外资企业': 'fas fa-globe',
        '合资企业': 'fas fa-handshake'
      }
      return iconMap[type] || 'fas fa-building'
    },

    // 获取合作状态样式类
    getStatusClass(status) {
      const statusMap = {
        '长期合作': 'long-term',
        '合作中': 'cooperating',
        '新客户': 'new-customer',
        '暂停合作': 'paused'
      }
      return statusMap[status] || 'default'
    },

    // 获取合作状态图标
    getStatusIcon(status) {
      const iconMap = {
        '长期合作': 'fas fa-star',
        '合作中': 'fas fa-handshake',
        '新客户': 'fas fa-user-plus',
        '暂停合作': 'fas fa-pause'
      }
      return iconMap[status] || 'fas fa-circle'
    }
  }
}
</script>

<style scoped>
.customer-view {
  padding: 20px;
  background: linear-gradient(135deg, #0a1929 0%, #1a2332 100%);
  min-height: calc(100vh - 140px);
  color: #ffffff;
}

/* 统计卡片样式 */
.stat-cards {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.stat-card {
  flex: 1;
  min-width: 200px;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.stat-card:hover {
  background: rgba(255, 255, 255, 0.12);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.stat-icon.blue { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.stat-icon.purple { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.stat-icon.green { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.stat-icon.orange { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }

.stat-content {
  flex: 1;
}

.stat-value {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 5px;
}

.stat-label {
  color: #8eb0d1;
  font-size: 14px;
}

.toggle-eye {
  cursor: pointer;
  font-size: 16px;
  color: #8eb0d1;
  transition: color 0.3s ease;
}

.toggle-eye:hover {
  color: #ffffff;
}

/* 主内容区域 */
.main-content {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 0;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 客户表格卡片 */
.customer-table-card {
  height: 100%;
}

.card-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.title-text {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
}

.table-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
}

.search-box i {
  position: absolute;
  left: 12px;
  color: #8eb0d1;
  font-size: 14px;
}

.search-box input {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  padding: 8px 12px 8px 35px;
  color: #ffffff;
  font-size: 14px;
  width: 200px;
  transition: all 0.3s ease;
}

.search-box input:focus {
  outline: none;
  border-color: #409eff;
  background: rgba(255, 255, 255, 0.15);
}

.search-box input::placeholder {
  color: #8eb0d1;
}

.refresh-btn {
  background: rgba(64, 158, 255, 0.2);
  border: 1px solid #409eff;
  border-radius: 6px;
  padding: 8px 12px;
  color: #409eff;
  cursor: pointer;
  transition: all 0.3s ease;
}

.refresh-btn:hover:not(:disabled) {
  background: rgba(64, 158, 255, 0.3);
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 客户表格样式 */
.customer-table {
  position: relative;
  max-height: 600px;
  overflow-y: auto;
}

.customer-table table {
  width: 100%;
  border-collapse: collapse;
}

.customer-table thead {
  position: sticky;
  top: 0;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  z-index: 10;
}

.customer-table th {
  padding: 15px 20px;
  text-align: left;
  font-weight: 600;
  color: #ffffff;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 14px;
}

.customer-table td {
  padding: 15px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  color: #e0e6ed;
  font-size: 14px;
}

.customer-table tbody tr {
  transition: all 0.3s ease;
  cursor: pointer;
}

.customer-table tbody tr:hover {
  background: rgba(255, 255, 255, 0.08);
}

.customer-table tbody tr.highlighted-row {
  background: rgba(64, 158, 255, 0.15);
  border-left: 3px solid #409eff;
}

/* 客户类型标签 */
.customer-type-tag {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.customer-type-tag.state-owned {
  background: rgba(255, 193, 7, 0.2);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.customer-type-tag.private {
  background: rgba(40, 167, 69, 0.2);
  color: #28a745;
  border: 1px solid rgba(40, 167, 69, 0.3);
}

.customer-type-tag.foreign {
  background: rgba(220, 53, 69, 0.2);
  color: #dc3545;
  border: 1px solid rgba(220, 53, 69, 0.3);
}

.customer-type-tag.joint-venture {
  background: rgba(102, 16, 242, 0.2);
  color: #6610f2;
  border: 1px solid rgba(102, 16, 242, 0.3);
}

/* 状态标签 */
.status-tag {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-tag.long-term {
  background: rgba(40, 167, 69, 0.2);
  color: #28a745;
  border: 1px solid rgba(40, 167, 69, 0.3);
}

.status-tag.cooperating {
  background: rgba(23, 162, 184, 0.2);
  color: #17a2b8;
  border: 1px solid rgba(23, 162, 184, 0.3);
}

.status-tag.new-customer {
  background: rgba(255, 193, 7, 0.2);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.status-tag.paused {
  background: rgba(108, 117, 125, 0.2);
  color: #6c757d;
  border: 1px solid rgba(108, 117, 125, 0.3);
}

/* 详情按钮 */
.detail-btn {
  background: rgba(64, 158, 255, 0.2);
  border: 1px solid #409eff;
  border-radius: 4px;
  padding: 6px 12px;
  color: #409eff;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 5px;
}

.detail-btn:hover {
  background: rgba(64, 158, 255, 0.3);
}

/* 表格加载状态 */
.table-loading {
  background: rgba(10, 25, 41, 0.8);
}

/* 客户详情弹窗样式 */
.customer-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(5px);
}

.customer-detail-container {
  background: linear-gradient(135deg, #1a2332 0%, #0a1929 100%);
  border-radius: 12px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  transform: scale(0.9);
  opacity: 0;
  transition: all 0.3s ease;
}

.customer-detail-container.show {
  transform: scale(1);
  opacity: 1;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
}

.customer-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 20px;
  font-weight: 600;
  color: #ffffff;
}

.close-button {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 6px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #8eb0d1;
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
}

.modal-content {
  padding: 25px;
  max-height: calc(90vh - 80px);
  overflow-y: auto;
}

/* 信息区块样式 */
.info-section {
  margin-bottom: 25px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.info-item {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.info-label {
  font-size: 12px;
  color: #8eb0d1;
  margin-bottom: 5px;
  font-weight: 500;
}

.info-value {
  font-size: 14px;
  color: #ffffff;
  font-weight: 500;
}

/* 弹窗中的小标签样式 */
.customer-type-tag-small,
.status-tag-small {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 3px 6px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: 500;
}

.customer-type-tag-small.state-owned,
.status-tag-small.long-term {
  background: rgba(40, 167, 69, 0.2);
  color: #28a745;
  border: 1px solid rgba(40, 167, 69, 0.3);
}

.customer-type-tag-small.private,
.status-tag-small.cooperating {
  background: rgba(23, 162, 184, 0.2);
  color: #17a2b8;
  border: 1px solid rgba(23, 162, 184, 0.3);
}

.customer-type-tag-small.foreign,
.status-tag-small.new-customer {
  background: rgba(255, 193, 7, 0.2);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.customer-type-tag-small.joint-venture,
.status-tag-small.paused {
  background: rgba(108, 117, 125, 0.2);
  color: #6c757d;
  border: 1px solid rgba(108, 117, 125, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stat-cards {
    flex-direction: column;
  }

  .stat-card {
    min-width: auto;
  }

  .table-actions {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }

  .search-box input {
    width: 100%;
  }

  .customer-table {
    font-size: 12px;
  }

  .customer-table th,
  .customer-table td {
    padding: 10px 15px;
  }

  .customer-detail-container {
    width: 95%;
    margin: 20px;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }
}

/* 滚动条样式 */
.customer-table::-webkit-scrollbar,
.modal-content::-webkit-scrollbar {
  width: 6px;
}

.customer-table::-webkit-scrollbar-track,
.modal-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.customer-table::-webkit-scrollbar-thumb,
.modal-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.customer-table::-webkit-scrollbar-thumb:hover,
.modal-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 全局弹窗样式 */
:global(.modal-open) {
  overflow: hidden;
}
</style>
